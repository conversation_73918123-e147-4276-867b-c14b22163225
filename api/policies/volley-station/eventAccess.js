module.exports = async function volleyStationEventAccess(req, res, next) {
    const eventId = parseInt(req.params.eventId);
    
    if (!eventId || isNaN(eventId)) {
        return res.validation('Invalid event identifier passed');
    }

    try {
        const hasAccess = await hasVolleyStationAccess(eventId);
        if (!hasAccess) {
            return res.status(403).send('This event is not configured for VolleyStation access');
        }
        next();
    } catch (err) {
        sails.log.error('VolleyStation event access error:', err);
        res.status(500).send('Unable to verify event access');
    }
};

async function hasVolleyStationAccess(eventId) {
    const eventQuery = knex('event as e')
        .select('event_id')
        .where('event_id', eventId)
        .where('enable_volley_station', true);

    const { rows } = await Db.query(eventQuery);
    return rows.length > 0;
}
